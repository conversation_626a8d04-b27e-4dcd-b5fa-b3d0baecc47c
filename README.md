# ClimaHealth AI Backend

A Node.js + MongoDB backend service for the ClimaHealth AI platform, which helps users manage asthma symptoms while considering climate conditions. The system includes features like symptom tracking, climate data monitoring, AI coaching, and gamification.

## Features

- User authentication and profile management
- Symptom logging and tracking
- Real-time climate data integration
- Asthma risk forecasting
- AI-powered health coaching
- Gamification system with rewards and challenges

## Prerequisites

- Node.js (v14 or higher)
- MongoDB database
- npm or yarn package manager

## Setup Instructions

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Create a `.env` file based on `.env.example` with your configuration:
   ```
   PORT=3000
   MONGODB_URI=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret
   ```
4. Start the development server:
   ```bash
   npm run dev
   ```
   Or for production:
   ```bash
   npm start
   ```

## API Endpoints

### Authentication
- `POST /auth/signup` - Register a new user
- `POST /auth/login` - Login user

### User Management
- `GET /user/profile` - Get user profile
- `PATCH /user/profile` - Update user profile

### Symptom Tracking
- `POST /symptom/log` - Create new symptom log
- `GET /symptom/history` - Get symptom history
- `GET /symptom/insights` - Get symptom insights

### Climate Data
- `GET /climate/current` - Get current climate data
- `GET /climate/latest` - Get latest climate readings

### Forecasting
- `GET /forecast/today` - Get today's asthma risk forecast

### AI Coach
- `POST /coach/message` - Send message to AI coach

### Gamification
- `POST /gamification/complete` - Complete a challenge
- `GET /gamification/rewards` - Get user rewards

## Data Models

### User
- Profile information
- Authentication details

### SymptomLog
- Wheezing level (0-10)
- Cough level (0-10)
- Asthma attack occurrence
- Notes
- Timestamp

### Conversation
- User messages
- AI coach responses
- Conversation history

### Reward
- User badges
- Points system
- Achievement tracking

## Environment Variables

| Variable | Description |
|----------|-------------|
| PORT | Server port number |
| MONGODB_URI | MongoDB connection string |
| JWT_SECRET | Secret for JWT token generation |

## Development

```bash
# Run in development mode with hot reload
npm run dev

# Run linter
npm run lint
```

## Dependencies

- express - Web framework
- mongoose - MongoDB ODM
- jsonwebtoken - JWT authentication
- bcryptjs - Password hashing
- cors - Cross-origin resource sharing
- helmet - Security headers
- dotenv - Environment configuration
- joi - Data validation
- axios - HTTP client
- morgan - HTTP request logger

## License

MIT
