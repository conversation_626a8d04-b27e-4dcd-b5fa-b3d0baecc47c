{"name": "climahealth-ai-backend", "version": "1.0.0", "description": "Node.js + MongoDB backend for ClimaHealth AI (asthma + climate assistant)", "main": "src/server.js", "type": "module", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "lint": "eslint ."}, "dependencies": {"axios": "^1.7.2", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.2", "morgan": "^1.10.0"}, "devDependencies": {"eslint": "^9.9.0", "nodemon": "^3.1.4"}}