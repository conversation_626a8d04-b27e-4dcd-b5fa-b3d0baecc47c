import SymptomLog from '../models/SymptomLog.js';
import ClimateData from '../models/ClimateData.js';

export async function createLog(req, res, next) {
  try {
    const { symptoms, notes, date } = req.body;
    const doc = await SymptomLog.create({ userId: req.user.id, symptoms, notes, date });
    res.status(201).json(doc);
  } catch (e) { next(e); }
}

export async function getHistory(req, res, next) {
  try {
    const logs = await SymptomLog.find({ userId: req.user.id }).sort({ date: -1 }).limit(100);
    res.json(logs);
  } catch (e) { next(e); }
}

// Very simple "insight" example: correlate attacks with AQI>100
export async function getInsights(req, res, next) {
  try {
    const logs = await SymptomLog.find({ userId: req.user.id }).sort({ date: -1 }).limit(200);
    const climates = await ClimateData.find().sort({ createdAt: -1 }).limit(500);

    let highAqiAttacks = 0, totalAttacks = 0;
    logs.forEach(l => {
      if (l.symptoms.attack) {
        totalAttacks++;
        // find nearest climate record by createdAt time
        const nearest = climates.find(c => Math.abs(new Date(c.createdAt) - new Date(l.date)) < 4 * 3600 * 1000);
        if (nearest && nearest.AQI >= 100) highAqiAttacks++;
      }
    });

    const insight = totalAttacks ? Math.round(100 * highAqiAttacks / totalAttacks) : 0;
    res.json({ message: `About ${insight}% of your recent attacks happened when AQI was >= 100.`, stats: { highAqiAttacks, totalAttacks } });
  } catch (e) { next(e); }
}
