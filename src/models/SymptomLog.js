import mongoose from 'mongoose';

const symptomLogSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  date: { type: Date, default: Date.now },
  symptoms: {
    wheezing: { type: Number, default: 0 }, // 0-10
    cough: { type: Number, default: 0 },    // 0-10
    attack: { type: Boolean, default: false }
  },
  notes: String
}, { timestamps: true });

export default mongoose.model('SymptomLog', symptomLogSchema);
